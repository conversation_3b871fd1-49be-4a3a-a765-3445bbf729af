# 项目监控大屏系统功能汇报

## 系统概述

本系统是一个基于Vue.js开发的智能化项目监控大屏系统，专为桥梁、建筑、隧道等基础设施监测项目设计。系统集成了3D可视化、实时数据监控、设备管理、视频监控等多项功能，为项目管理提供全方位的数字化解决方案。

**访问地址**: http://localhost:2023/#/screen?projectId=16&projectName=中铁项目2

## 核心功能模块

### 1. 项目总览大屏 (NewProjectDetailView)

#### 1.1 智能化仪表盘
- **实时时间显示**: 精确到秒的时间显示，支持全屏模式
- **项目基础信息**: 项目名称、位置、进度、状态等关键信息展示
- **响应式布局**: 自适应不同屏幕尺寸，支持全屏无边距显示
- **科技感UI设计**: 采用深蓝色星空背景，科技风格界面元素

#### 1.2 模块化布局
- **左侧模块**: 报警信息、姿态水平状态、传感器数据
- **中央模块**: 3D模型展示区域
- **右侧模块**: 环境监控、摄像头管理
- **顶部模块**: 天气信息、导航控制

### 2. 3D可视化展示系统 (CenterColumn)

#### 2.1 3D模型渲染
- **模型加载**: 支持GLTF/GLB格式3D模型，使用THREE.js渲染引擎
- **模型来源**: 
  - 本地模型: `/models/xsqt.glb`
  - 远程模型: `http://glb.bjbrtt.com/xsqt.glb`
  - 备用模型: 自动生成几何体模型
- **交互控制**: 
  - 鼠标拖拽旋转
  - 滚轮缩放
  - 自动旋转（可暂停5分钟）
  - 点击暂停自动旋转功能

#### 2.2 传感器可视化
- **传感器标记**: 在3D模型表面显示传感器位置
- **信息标签**: 透明背景的传感器信息显示框
- **实时数据**: 传感器数据实时更新显示
- **交互优化**: 标签位置优化，避免遮挡模型视图

#### 2.3 渲染优化
- **透明背景**: 优化显示效果，减少页面卡顿
- **DRACOLoader**: 支持压缩模型加载
- **性能优化**: 智能渲染循环，降低GPU负载

### 3. 设备管理系统 (DeviceManagement)

#### 3.1 设备统计面板
- **设备概览**: 总设备数、在线设备、离线设备、传感器总数
- **实时状态**: 设备在线状态实时监控
- **设备分类**: 按设备类型分类显示

#### 3.2 设备详情管理
- **设备列表**: 显示项目下所有设备信息
- **网关管理**: 每个设备的网关信息展示
- **传感器管理**: 设备下属传感器详细信息
- **3D模型集成**: 根据设备类型加载对应3D模型

#### 3.3 数据接口
- **API端点**: `/api/system/sensor/gateway/code/{gatewayCode}/all-sensors`
- **数据更新**: 30秒自动刷新传感器数据
- **设备模型映射**: 
  - 桥梁监测: `http://glb.bjbrtt.com/xsqt.glb`
  - 建筑监测: `http://glb.bjbrtt.com/building.glb`
  - 隧道监测: `http://glb.bjbrtt.com/tunnel.glb`

### 4. 视频监控系统 (CameraManagement)

#### 4.1 摄像头管理
- **设备关联**: 按设备分组显示摄像头
- **摄像头类型**: 
  - 1-球机(dome)
  - 2-枪机(bullet) 
  - 3-半球(hemisphere)
  - 4-其他(other)
- **状态监控**: 在线/离线状态实时显示

#### 4.2 视频播放
- **Video.js集成**: 使用Vue 2兼容的Video.js播放器
- **自动播放**: 进入页面自动加载并播放视频
- **弹窗详情**: 点击摄像头显示详细信息弹窗
- **滚动支持**: 摄像头数量过多时显示滚动条

#### 4.3 技术特性
- **复用组件**: 使用现有CameraDetailModal.vue组件
- **科技风格**: 与设备管理模块差异化显示
- **性能优化**: 按需加载视频流

### 5. 环境监控系统 (EnvironmentChart)

#### 5.1 环境数据监控
- **数据源**: `getProjectEnvironmentData(projectId)` API
- **监控参数**: 温度、湿度、压力、风速等环境参数
- **更新频率**: 每30秒自动更新
- **显示格式**: 3项/行的紧凑显示布局

#### 5.2 数据可视化
- **图表展示**: 使用Chart.js进行数据可视化
- **实时曲线**: 环境参数变化趋势图
- **阈值告警**: 超出正常范围时的告警提示

### 6. 报警管理系统 (AlarmTable)

#### 6.1 报警信息展示
- **数据源**: `getProjectAlarmsData(projectId)` API
- **实时更新**: 报警信息实时刷新显示
- **状态管理**: 支持报警状态更新
- **更新接口**: `/api/system/alarms/update/{id}`

#### 6.2 报警处理
- **分级显示**: 不同级别报警的差异化显示
- **处理状态**: 已处理/未处理状态标识
- **操作记录**: 报警处理操作日志

### 7. 姿态监控系统 (AttitudeLevel)

#### 7.1 姿态水平监测
- **数据源**: `getProjectAngleData(projectId)` API
- **监测参数**: X轴、Y轴倾斜角度
- **显示范围**: -3°到+3°的精确显示
- **可视化**: 水平条和指针位置显示

#### 7.2 实时更新
- **更新频率**: 实时数据更新
- **数据模拟**: 支持演示数据生成
- **状态指示**: 倾斜状态的直观显示

### 8. 天气信息系统 (WeatherInfo)

#### 8.1 天气数据展示
- **API接口**: `api/system/weather.js`
- **数据源**: `/api/system/project-weather/latest-by-number/${projectId}`
- **显示信息**: 温度、湿度、气压、风速、能见度
- **动态更新**: 天气信息实时更新

#### 8.2 界面集成
- **头部显示**: 集成在系统头部区域
- **无边框设计**: 与整体界面风格统一
- **大字体显示**: 便于远距离观看

### 9. 地图导航系统 (Map3D)

#### 9.1 3D地图展示
- **地图数据**: 支持GeoJSON格式地图数据
- **3D渲染**: 基于THREE.js的3D地图渲染
- **项目标记**: 在地图上标记项目位置
- **交互操作**: 支持地图缩放、旋转、平移

#### 9.2 导航功能
- **返回地图**: 支持返回地图页面
- **返回中国地图**: 支持返回全国地图视图
- **项目切换**: 地图上项目点击切换功能

## 技术架构

### 前端技术栈
- **框架**: Vue.js 2.x
- **3D渲染**: THREE.js + GLTFLoader + DRACOLoader
- **视频播放**: Video.js
- **图表库**: Chart.js 4.4.9
- **UI组件**: Element UI
- **样式**: CSS3 + 科技风格设计

### 后端接口
- **基础API**: `http://39.107.230.93:3066`
- **项目数据**: `/api/project/*`
- **设备数据**: `/api/device/*`
- **传感器数据**: `/api/sensor/*`
- **环境数据**: `/api/system/environment/*`
- **报警数据**: `/api/system/alarms/*`
- **天气数据**: `/api/system/project-weather/*`

### 性能优化
- **响应式设计**: 自适应不同屏幕尺寸
- **懒加载**: 按需加载3D模型和视频流
- **数据缓存**: 合理的数据更新频率
- **渲染优化**: 透明背景、智能渲染循环

## 系统特色

### 1. 全屏体验
- **无边距显示**: 全屏模式下内容铺满整个屏幕
- **隐藏导航**: 全屏时自动隐藏左侧导航
- **响应式缩放**: 根据屏幕尺寸自动调整界面元素

### 2. 科技感设计
- **深蓝星空背景**: 营造科技感氛围
- **透明边框**: 模块间使用透明边框设计
- **光晕效果**: 添加科技感光晕特效
- **图标集成**: Font Awesome图标库

### 3. 实时性保障
- **数据实时更新**: 多个模块支持实时数据刷新
- **状态同步**: 设备、传感器状态实时同步
- **告警及时**: 异常情况及时告警提示

### 4. 交互体验
- **直观操作**: 3D模型支持鼠标交互
- **模块切换**: 支持仪表盘、设备管理、摄像头管理模块切换
- **弹窗详情**: 点击元素显示详细信息弹窗

## 部署信息

- **开发环境**: http://localhost:2023
- **生产环境**: 配置在 `.env.production`
- **项目路径**: `d:\space\brtt\bridge-cloud-client`
- **构建工具**: Vue CLI + Webpack

## 总结

本项目监控大屏系统成功整合了3D可视化、实时数据监控、设备管理、视频监控等多项先进技术，为基础设施监测项目提供了一个功能完善、界面美观、交互友好的数字化监控平台。系统具备良好的扩展性和维护性，能够满足不同规模项目的监控需求。
